@import "tailwindcss";

/* Font definitions are handled by Next.js font optimization in src/lib/fonts/index.ts */

:root {
  --background: 248 250 252;
  --foreground: 0 0 0;

  --card: 255 255 255;
  --card-foreground: 30 41 59;

  --popover: 255 255 255;
  --popover-foreground: 30 41 59;

  --primary: 194 100 24; /* #00617b */
  --primary-light: 194 69 43; /* #338ca8 */
  --primary-dark: 194 100 17; /* #004659 */
  --primary-foreground: 255 255 255;

  --secondary: 0 0 0;
  --secondary-foreground: 71 85 105;

  --muted: 241 245 249;
  --muted-foreground: 100 116 139;

  --accent: 194 48 94; /* light variant of primary */
  --accent-foreground: 194 100 24;

  --destructive: 239 68 68;
  --destructive-foreground: 255 255 255;

  --border: 0 0 0;
  --input: 226 232 240;
  --ring: 194 100 24;

  --chart-1: oklch(0.75 0.15 200); /* teal-blue */
  --chart-2: oklch(0.68 0.13 210); /* ocean-blue */
  --chart-3: oklch(0.50 0.10 220); /* navy-muted */
  /* removed yellow chart-4 and chart-5 */

  --sidebar: 255 255 255;
  --sidebar-foreground: 71 85 105;
  --sidebar-primary: 194 100 24;
  --sidebar-primary-foreground: 255 255 255;
  --sidebar-accent: 194 48 94;
  --sidebar-accent-foreground: 194 100 24;
  --sidebar-border: 226 232 240;
  --sidebar-ring: 194 100 24;

  --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif,
    "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
    "Liberation Mono", "Courier New", monospace;

  --radius: 0.625rem;

  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.1),
    0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.1),
    0 2px 4px -1px hsl(0 0% 0% / 0.1);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.1),
    0 4px 6px -1px hsl(0 0% 0% / 0.1);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.1),
    0 8px 10px -1px hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}


@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.animate-scroll {
  animation: scroll 30s linear infinite;
}

.animate-scroll:hover {
  animation-play-state: paused;
}

/* Responsive utilities */
@layer utilities {
  /* Container padding adjustments for mobile */
  .container-responsive {
    @apply px-4 sm:px-6 lg:px-8;
  }

  /* Responsive grid gaps */
  .grid-responsive {
    @apply gap-4 sm:gap-6 lg:gap-8;
  }

  /* Responsive text sizes */
  .text-responsive-xs {
    @apply text-xs sm:text-sm;
  }

  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }

  .text-responsive-base {
    @apply text-base sm:text-lg;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl lg:text-2xl;
  }

  .text-responsive-xl {
    @apply text-xl sm:text-2xl lg:text-3xl;
  }

  .text-responsive-2xl {
    @apply text-2xl sm:text-3xl lg:text-4xl;
  }

  .text-responsive-3xl {
    @apply text-3xl sm:text-4xl lg:text-5xl;
  }

  /* Responsive spacing */
  .space-responsive {
    @apply space-y-4 sm:space-y-6 lg:space-y-8;
  }

  .p-responsive {
    @apply p-4 sm:p-6 lg:p-8;
  }

  .px-responsive {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .py-responsive {
    @apply py-4 sm:py-6 lg:py-8;
  }

  /* Mobile-first card padding */
  .card-responsive {
    @apply p-4 sm:p-6 lg:p-8;
  }

  /* Responsive flex direction */
  .flex-responsive {
    @apply flex flex-col sm:flex-row;
  }

  /* Hide on mobile */
  .hidden-mobile {
    @apply hidden sm:block;
  }

  /* Show only on mobile */
  .mobile-only {
    @apply block sm:hidden;
  }

  /* Responsive button sizes */
  .btn-responsive {
    @apply px-4 py-2 sm:px-6 sm:py-3;
  }

  /* Responsive form inputs */
  .input-responsive {
    @apply w-full px-3 py-2 sm:px-4 sm:py-3;
  }

  /* Safe area padding for mobile devices with notches */
  .safe-area-padding {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Responsive table wrapper */
  .table-responsive {
    @apply overflow-x-auto -mx-4 sm:mx-0;
  }

  .table-responsive table {
    @apply min-w-full;
  }

  /* Mobile-friendly modal/dialog */
  .modal-responsive {
    @apply w-full max-w-lg mx-4 sm:mx-auto;
  }

  /* Responsive sidebar width */
  .sidebar-responsive {
    @apply w-full sm:w-64 lg:w-72;
  }

  /* Sidebar responsive improvements */
  .sidebar-container {
    @apply flex min-h-screen w-full overflow-hidden;
  }

  .sidebar-main-content {
    @apply flex-1 flex flex-col min-w-0 w-full;
  }

  /* Prevent sidebar overlap on smaller screens */
  @media (min-width: 1024px) and (max-width: 1279px) {
    :root {
      --sidebar-width-responsive: 14rem;
    }
  }

  @media (min-width: 1280px) {
    :root {
      --sidebar-width-responsive: 16rem;
    }
  }

  /* Mobile-optimized form layouts */
  .form-responsive {
    @apply space-y-4 sm:space-y-6;
  }

  .form-responsive .form-group {
    @apply flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-2 sm:space-y-0;
  }

  .form-responsive label {
    @apply text-sm font-medium sm:w-32 sm:flex-shrink-0;
  }

  .form-responsive input,
  .form-responsive select,
  .form-responsive textarea {
    @apply w-full sm:flex-1;
  }

  /* Mobile-friendly navigation */
  .nav-responsive {
    @apply flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4;
  }

  /* Responsive card grids */
  .cards-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6;
  }

  /* Mobile-optimized dashboard layout */
  .dashboard-responsive {
    @apply flex flex-col lg:flex-row min-h-screen;
  }

  .dashboard-content {
    @apply flex-1 p-4 sm:p-6 lg:p-8 overflow-auto;
  }

  /* Responsive typography scale */
  .heading-responsive-1 {
    @apply text-2xl sm:text-3xl lg:text-4xl font-bold;
  }

  .heading-responsive-2 {
    @apply text-xl sm:text-2xl lg:text-3xl font-semibold;
  }

  .heading-responsive-3 {
    @apply text-lg sm:text-xl lg:text-2xl font-medium;
  }

  /* Mobile-first button groups */
  .button-group-responsive {
    @apply flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2;
  }

  /* Responsive image containers */
  .image-responsive {
    @apply w-full h-auto object-cover;
  }

  /* Mobile-optimized list layouts */
  .list-responsive {
    @apply space-y-2 sm:space-y-3;
  }

  .list-item-responsive {
    @apply flex flex-col sm:flex-row sm:items-center sm:justify-between p-3 sm:p-4;
  }
}

/* Mobile-specific overrides */
@media (max-width: 640px) {
  /* Reduce padding on small screens */
  .container {
    @apply px-4;
  }

  /* Stack navigation items vertically on mobile */
  .nav-horizontal {
    @apply flex-col space-y-2 space-x-0;
  }

  /* Full-width buttons on mobile */
  .btn-mobile-full {
    @apply w-full;
  }

  /* Smaller text on mobile for better readability */
  .text-mobile-sm {
    @apply text-sm;
  }

  /* Hide complex layouts on mobile */
  .complex-layout {
    @apply hidden;
  }

  /* Show simplified mobile layout */
  .mobile-layout {
    @apply block;
  }
}

/* Tablet-specific adjustments */
@media (min-width: 641px) and (max-width: 1024px) {
  /* Tablet-optimized grid */
  .tablet-grid {
    @apply grid-cols-2;
  }

  /* Tablet sidebar */
  .tablet-sidebar {
    @apply w-56;
  }
}

/* Desktop enhancements */
@media (min-width: 1025px) {
  /* Desktop-specific hover effects */
  .desktop-hover:hover {
    @apply transform scale-105 transition-transform duration-200;
  }

  /* Desktop grid layouts */
  .desktop-grid {
    @apply grid-cols-4;
  }
}
